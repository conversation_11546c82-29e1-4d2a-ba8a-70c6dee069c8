"""
Analytics API endpoints.
"""

from typing import List, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from sqlalchemy import func

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.models.email import Email
from app.models.analytics import UserAnalytics, SenderAnalytics

router = APIRouter()


@router.get("/dashboard")
async def get_dashboard_analytics(
    days: int = Query(30, ge=1, le=365),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get dashboard analytics for the specified number of days."""
    end_date = datetime.utcnow().date()
    start_date = end_date - timedelta(days=days)
    
    # Get daily analytics
    daily_analytics = db.query(UserAnalytics).filter(
        UserAnalytics.user_id == current_user.id,
        UserAnalytics.date >= start_date,
        UserAnalytics.date <= end_date
    ).order_by(UserAnalytics.date).all()
    
    # Calculate summary metrics
    total_received = sum(a.emails_received for a in daily_analytics)
    total_sent = sum(a.emails_sent for a in daily_analytics)
    avg_response_time = sum(a.avg_response_time_hours or 0 for a in daily_analytics) / len(daily_analytics) if daily_analytics else 0
    
    return {
        "period": {
            "start_date": start_date,
            "end_date": end_date,
            "days": days
        },
        "summary": {
            "total_received": total_received,
            "total_sent": total_sent,
            "avg_response_time_hours": round(avg_response_time, 2),
            "daily_average_received": round(total_received / days, 1),
            "daily_average_sent": round(total_sent / days, 1)
        },
        "daily_data": [
            {
                "date": a.date,
                "emails_received": a.emails_received,
                "emails_sent": a.emails_sent,
                "emails_processed": a.emails_processed,
                "response_time_hours": a.avg_response_time_hours,
                "category_distribution": a.category_distribution,
                "sentiment_distribution": a.sentiment_distribution
            }
            for a in daily_analytics
        ]
    }


@router.get("/trends")
async def get_email_trends(
    days: int = Query(30, ge=7, le=365),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get email volume and pattern trends."""
    end_date = datetime.utcnow().date()
    start_date = end_date - timedelta(days=days)
    
    # Get email volume by day
    daily_volumes = db.query(
        func.date(Email.received_at).label('date'),
        func.count(Email.id).label('count')
    ).filter(
        Email.user_id == current_user.id,
        func.date(Email.received_at) >= start_date
    ).group_by(func.date(Email.received_at)).all()
    
    # Get hourly distribution
    hourly_distribution = db.query(
        func.extract('hour', Email.received_at).label('hour'),
        func.count(Email.id).label('count')
    ).filter(
        Email.user_id == current_user.id,
        func.date(Email.received_at) >= start_date
    ).group_by(func.extract('hour', Email.received_at)).all()
    
    # Get day of week distribution
    dow_distribution = db.query(
        func.extract('dow', Email.received_at).label('dow'),
        func.count(Email.id).label('count')
    ).filter(
        Email.user_id == current_user.id,
        func.date(Email.received_at) >= start_date
    ).group_by(func.extract('dow', Email.received_at)).all()
    
    return {
        "daily_volumes": [
            {"date": str(dv.date), "count": dv.count}
            for dv in daily_volumes
        ],
        "hourly_distribution": [
            {"hour": int(hd.hour), "count": hd.count}
            for hd in hourly_distribution
        ],
        "day_of_week_distribution": [
            {"day": int(dow.dow), "count": dow.count}
            for dow in dow_distribution
        ]
    }


@router.get("/senders")
async def get_sender_analytics(
    limit: int = Query(20, ge=1, le=100),
    days: int = Query(30, ge=1, le=365),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get top senders analytics."""
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days)
    
    # Get top senders by email count
    top_senders = db.query(
        Email.sender_email,
        Email.sender_name,
        func.count(Email.id).label('email_count'),
        func.avg(func.case([(Email.is_read == True, 1)], else_=0)).label('read_rate'),
        func.max(Email.received_at).label('last_email')
    ).filter(
        Email.user_id == current_user.id,
        Email.received_at >= start_date
    ).group_by(
        Email.sender_email, Email.sender_name
    ).order_by(
        func.count(Email.id).desc()
    ).limit(limit).all()
    
    return {
        "top_senders": [
            {
                "sender_email": sender.sender_email,
                "sender_name": sender.sender_name,
                "email_count": sender.email_count,
                "read_rate": round(sender.read_rate * 100, 1),
                "last_email": sender.last_email
            }
            for sender in top_senders
        ]
    }


@router.get("/categories")
async def get_category_analytics(
    days: int = Query(30, ge=1, le=365),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get email category distribution analytics."""
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days)
    
    # Get category distribution
    category_stats = db.query(
        Email.category,
        func.count(Email.id).label('count'),
        func.avg(func.case([(Email.is_read == True, 1)], else_=0)).label('read_rate'),
        func.avg(Email.category_confidence).label('avg_confidence')
    ).filter(
        Email.user_id == current_user.id,
        Email.received_at >= start_date,
        Email.category.isnot(None)
    ).group_by(Email.category).all()
    
    # Get sentiment distribution
    sentiment_stats = db.query(
        Email.sentiment,
        func.count(Email.id).label('count'),
        func.avg(Email.sentiment_score).label('avg_score')
    ).filter(
        Email.user_id == current_user.id,
        Email.received_at >= start_date,
        Email.sentiment.isnot(None)
    ).group_by(Email.sentiment).all()
    
    return {
        "category_distribution": [
            {
                "category": stat.category.value if stat.category else "unknown",
                "count": stat.count,
                "read_rate": round(stat.read_rate * 100, 1),
                "avg_confidence": round(stat.avg_confidence or 0, 3)
            }
            for stat in category_stats
        ],
        "sentiment_distribution": [
            {
                "sentiment": stat.sentiment.value if stat.sentiment else "unknown",
                "count": stat.count,
                "avg_score": round(stat.avg_score or 0, 3)
            }
            for stat in sentiment_stats
        ]
    }


@router.get("/productivity")
async def get_productivity_metrics(
    days: int = Query(30, ge=1, le=365),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get productivity metrics."""
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days)
    
    # Calculate response metrics
    emails_requiring_response = db.query(Email).filter(
        Email.user_id == current_user.id,
        Email.received_at >= start_date,
        Email.requires_response == True
    ).count()
    
    emails_responded = db.query(Email).filter(
        Email.user_id == current_user.id,
        Email.received_at >= start_date,
        Email.is_responded == True
    ).count()
    
    # Calculate inbox zero days
    inbox_zero_days = db.query(UserAnalytics).filter(
        UserAnalytics.user_id == current_user.id,
        UserAnalytics.date >= start_date.date(),
        UserAnalytics.inbox_zero_achieved == True
    ).count()
    
    # Get average response time
    avg_response_time = db.query(
        func.avg(UserAnalytics.avg_response_time_hours)
    ).filter(
        UserAnalytics.user_id == current_user.id,
        UserAnalytics.date >= start_date.date()
    ).scalar() or 0
    
    return {
        "response_metrics": {
            "emails_requiring_response": emails_requiring_response,
            "emails_responded": emails_responded,
            "response_rate": round(emails_responded / emails_requiring_response * 100, 1) if emails_requiring_response > 0 else 0,
            "avg_response_time_hours": round(avg_response_time, 2)
        },
        "productivity_metrics": {
            "inbox_zero_days": inbox_zero_days,
            "inbox_zero_rate": round(inbox_zero_days / days * 100, 1),
            "total_days": days
        }
    }
