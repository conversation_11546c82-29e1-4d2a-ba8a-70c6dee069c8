"""
Email account model for managing multiple email provider connections.
"""

from sqlalchemy import <PERSON>umn, Inte<PERSON>, String, Boolean, DateTime, Text, ForeignKey, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum

from app.core.database import Base


class EmailProvider(enum.Enum):
    """Email provider types."""
    GMAIL = "gmail"
    OUTLOOK = "outlook"
    IMAP = "imap"
    EXCHANGE = "exchange"


class EmailAccount(Base):
    """Email account model for storing email provider connections."""
    
    __tablename__ = "email_accounts"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Account details
    email_address = Column(String(255), nullable=False, index=True)
    display_name = Column(String(255), nullable=True)
    provider = Column(Enum(EmailProvider), nullable=False)
    
    # Connection settings
    is_active = Column(Boolean, default=True)
    is_primary = Column(<PERSON><PERSON><PERSON>, default=False)
    
    # OAuth tokens (encrypted)
    access_token = Column(Text, nullable=True)
    refresh_token = Column(Text, nullable=True)
    token_expires_at = Column(DateTime(timezone=True), nullable=True)
    
    # IMAP/SMTP settings (for non-OAuth providers)
    imap_server = Column(String(255), nullable=True)
    imap_port = Column(Integer, nullable=True)
    imap_use_ssl = Column(Boolean, default=True)
    smtp_server = Column(String(255), nullable=True)
    smtp_port = Column(Integer, nullable=True)
    smtp_use_ssl = Column(Boolean, default=True)
    
    # Sync settings
    last_sync_at = Column(DateTime(timezone=True), nullable=True)
    sync_enabled = Column(Boolean, default=True)
    sync_frequency = Column(Integer, default=300)  # seconds
    
    # Folder/label mapping
    inbox_folder = Column(String(255), default="INBOX")
    sent_folder = Column(String(255), default="Sent")
    trash_folder = Column(String(255), default="Trash")
    spam_folder = Column(String(255), default="Spam")
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="email_accounts")
    emails = relationship("Email", back_populates="email_account", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<EmailAccount(id={self.id}, email='{self.email_address}', provider='{self.provider.value}')>"
