"""
Models package for the email management system.
"""

# Import all models to ensure they are registered with SQLAlchemy
from app.models.user import User
from app.models.email_account import EmailAccount, EmailProvider
from app.models.email import (
    Email,
    EmailAttachment,
    EmailStatus,
    EmailCategory,
    EmailSentiment
)
from app.models.classification import (
    ClassificationRule,
    VIPSender,
    SmartReply,
    EmailTemplate,
    RuleCondition,
    RuleAction
)
from app.models.analytics import (
    UserAnalytics,
    SenderAnalytics,
    EmailThread,
    SystemMetrics
)

__all__ = [
    # User models
    "User",

    # Email account models
    "EmailAccount",
    "EmailProvider",

    # Email models
    "Email",
    "EmailAttachment",
    "EmailStatus",
    "EmailCategory",
    "EmailSentiment",

    # Classification models
    "ClassificationRule",
    "VIPSender",
    "SmartReply",
    "EmailTemplate",
    "RuleCondition",
    "RuleAction",

    # Analytics models
    "UserAnalytics",
    "SenderAnalytics",
    "EmailThread",
    "SystemMetrics",
]