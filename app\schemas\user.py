"""
User schemas for API requests and responses.
"""

from typing import Optional
from datetime import datetime
from pydantic import BaseModel, EmailStr

from app.models.email_account import EmailProvider


class UserResponse(BaseModel):
    """User response schema."""
    id: int
    email: EmailStr
    full_name: Optional[str]
    is_active: bool
    timezone: str
    language: str
    email_sync_enabled: bool
    auto_classification: bool
    smart_replies: bool
    created_at: datetime
    last_login: Optional[datetime]
    
    class Config:
        from_attributes = True


class UserUpdate(BaseModel):
    """User update schema."""
    full_name: Optional[str] = None
    timezone: Optional[str] = None
    language: Optional[str] = None
    email_sync_enabled: Optional[bool] = None
    auto_classification: Optional[bool] = None
    smart_replies: Optional[bool] = None


class EmailAccountCreate(BaseModel):
    """Email account creation schema."""
    email_address: EmailStr
    display_name: Optional[str] = None
    provider: EmailProvider
    
    # For IMAP accounts
    imap_server: Optional[str] = None
    imap_port: Optional[int] = None
    imap_use_ssl: Optional[bool] = True
    smtp_server: Optional[str] = None
    smtp_port: Optional[int] = None
    smtp_use_ssl: Optional[bool] = True
    
    # OAuth tokens (will be set during OAuth flow)
    access_token: Optional[str] = None
    refresh_token: Optional[str] = None


class EmailAccountResponse(BaseModel):
    """Email account response schema."""
    id: int
    email_address: str
    display_name: Optional[str]
    provider: EmailProvider
    is_active: bool
    is_primary: bool
    sync_enabled: bool
    sync_frequency: int
    last_sync_at: Optional[datetime]
    created_at: datetime
    
    class Config:
        from_attributes = True
