"""
Classification models for email categorization and rules.
"""

from sqlalchemy import <PERSON>um<PERSON>, Inte<PERSON>, String, <PERSON>olean, DateTime, Text, ForeignKey, Enum, JSON, Float
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum

from app.core.database import Base
from app.models.email import EmailCategory


class RuleCondition(enum.Enum):
    """Rule condition types."""
    SENDER_CONTAINS = "sender_contains"
    SENDER_EQUALS = "sender_equals"
    SUBJECT_CONTAINS = "subject_contains"
    SUBJECT_EQUALS = "subject_equals"
    BODY_CONTAINS = "body_contains"
    HAS_ATTACHMENT = "has_attachment"
    RECIPIENT_CONTAINS = "recipient_contains"
    DOMAIN_EQUALS = "domain_equals"


class RuleAction(enum.Enum):
    """Rule action types."""
    CATEGORIZE = "categorize"
    MOVE_TO_FOLDER = "move_to_folder"
    ADD_LABEL = "add_label"
    MARK_IMPORTANT = "mark_important"
    MARK_READ = "mark_read"
    DELETE = "delete"
    FORWARD = "forward"


class ClassificationRule(Base):
    """User-defined rules for email classification and organization."""
    
    __tablename__ = "classification_rules"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Rule details
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True)
    priority = Column(Integer, default=0)  # Higher number = higher priority
    
    # Conditions (JSON array of condition objects)
    conditions = Column(JSON, nullable=False)
    # Example: [{"field": "sender_email", "operator": "contains", "value": "@company.com"}]
    
    # Actions (JSON array of action objects)
    actions = Column(JSON, nullable=False)
    # Example: [{"type": "categorize", "value": "work"}, {"type": "add_label", "value": "important"}]
    
    # Statistics
    times_applied = Column(Integer, default=0)
    last_applied_at = Column(DateTime(timezone=True), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="classification_rules")
    
    def __repr__(self):
        return f"<ClassificationRule(id={self.id}, name='{self.name}', user_id={self.user_id})>"


class VIPSender(Base):
    """VIP sender management for priority handling."""
    
    __tablename__ = "vip_senders"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Sender details
    email_address = Column(String(255), nullable=False, index=True)
    name = Column(String(255), nullable=True)
    
    # VIP settings
    priority_level = Column(Integer, default=1)  # 1=high, 2=medium, 3=low
    auto_reply_enabled = Column(Boolean, default=False)
    auto_reply_template = Column(Text, nullable=True)
    
    # Notifications
    instant_notification = Column(Boolean, default=True)
    notification_sound = Column(String(100), nullable=True)
    
    # Statistics
    email_count = Column(Integer, default=0)
    last_email_at = Column(DateTime(timezone=True), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User")
    
    def __repr__(self):
        return f"<VIPSender(id={self.id}, email='{self.email_address}', priority={self.priority_level})>"


class SmartReply(Base):
    """AI-generated smart reply suggestions."""
    
    __tablename__ = "smart_replies"
    
    id = Column(Integer, primary_key=True, index=True)
    email_id = Column(Integer, ForeignKey("emails.id"), nullable=False)
    
    # Reply content
    reply_text = Column(Text, nullable=False)
    reply_type = Column(String(50), nullable=True)  # "short", "medium", "long", "formal", "casual"
    
    # AI metadata
    confidence_score = Column(Float, nullable=True)
    model_version = Column(String(50), nullable=True)
    
    # Usage tracking
    is_used = Column(Boolean, default=False)
    used_at = Column(DateTime(timezone=True), nullable=True)
    user_rating = Column(Integer, nullable=True)  # 1-5 stars
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    email = relationship("Email", back_populates="smart_replies")
    
    def __repr__(self):
        return f"<SmartReply(id={self.id}, email_id={self.email_id}, type='{self.reply_type}')>"


class EmailTemplate(Base):
    """User-defined email templates."""
    
    __tablename__ = "email_templates"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Template details
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    category = Column(String(100), nullable=True)
    
    # Template content
    subject_template = Column(String(500), nullable=True)
    body_template = Column(Text, nullable=False)
    
    # Template variables (JSON array)
    variables = Column(JSON, nullable=True)
    # Example: ["recipient_name", "company_name", "date"]
    
    # Usage tracking
    times_used = Column(Integer, default=0)
    last_used_at = Column(DateTime(timezone=True), nullable=True)
    
    # Settings
    is_active = Column(Boolean, default=True)
    is_public = Column(Boolean, default=False)  # Can be shared with other users
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User")
    
    def __repr__(self):
        return f"<EmailTemplate(id={self.id}, name='{self.name}', user_id={self.user_id})>"
