# Email Management System

A comprehensive AI-powered email management system that connects to Gmail, Outlook, and other email providers to automatically organize, prioritize, and respond to emails using natural language processing and machine learning.

## Features

### Core Functionality
- Multi-provider email integration (Gmail API, Outlook Graph API, IMAP)
- Intelligent email classification using NLP
- Automated folder/label organization with custom rules
- AI-powered email summarization for long threads
- Smart reply suggestions based on context and writing style
- Follow-up reminder system with customizable intervals
- VIP sender detection and priority handling

### Advanced AI Features
- Sentiment analysis for urgent/negative email detection
- Entity extraction for calendar integration
- Duplicate email detection and cleanup
- Language detection and auto-translation
- Attachment analysis and filing
- Meeting request extraction and scheduling
- Email template generation

### Analytics Dashboard
- Email volume trends and response time analytics
- Communication patterns analysis
- Time-of-day email habits
- Productivity impact measurements
- Weekly/monthly email health reports
- Custom KPI tracking

### Security & Privacy
- End-to-end encryption for stored data
- OAuth 2.0 for secure email provider authentication
- Local data processing options
- GDPR compliance features
- Two-factor authentication

## Tech Stack

- **Backend**: FastAPI, SQLAlchemy, Celery
- **Frontend**: Streamlit
- **Database**: PostgreSQL
- **Cache**: Redis
- **NLP/ML**: spaCy, NLTK, scikit-learn, Transformers
- **Email APIs**: Gmail API, Microsoft Graph API, IMAP

## Installation

1. Clone the repository
2. Create a virtual environment: `python -m venv venv`
3. Activate the virtual environment: `source venv/bin/activate` (Linux/Mac) or `venv\Scripts\activate` (Windows)
4. Install dependencies: `pip install -r requirements.txt`
5. Copy `.env.example` to `.env` and configure your settings
6. Run database migrations: `alembic upgrade head`
7. Start the services:
   - Backend API: `uvicorn app.main:app --reload`
   - Streamlit Dashboard: `streamlit run dashboard/main.py`
   - Celery Worker: `celery -A app.celery worker --loglevel=info`

## Project Structure

```
email_management/
├── app/                    # FastAPI backend
│   ├── api/               # API routes
│   ├── core/              # Core configuration
│   ├── models/            # SQLAlchemy models
│   ├── services/          # Business logic
│   └── utils/             # Utility functions
├── dashboard/             # Streamlit frontend
├── alembic/              # Database migrations
├── tests/                # Test files
└── docs/                 # Documentation
```

## License

MIT License
