version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: email_management
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U user -d email_management"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # FastAPI Backend
  api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**********************************/email_management
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-here
      - ENCRYPTION_KEY=your-encryption-key-here
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - .:/app
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # Streamlit Dashboard
  dashboard:
    build: .
    ports:
      - "8501:8501"
    environment:
      - DATABASE_URL=**********************************/email_management
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - api
    volumes:
      - .:/app
    command: streamlit run dashboard/main.py --server.port 8501 --server.address 0.0.0.0

  # Celery Worker
  worker:
    build: .
    environment:
      - DATABASE_URL=**********************************/email_management
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-here
      - ENCRYPTION_KEY=your-encryption-key-here
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - .:/app
    command: celery -A app.core.celery_app worker --loglevel=info

  # Celery Beat (Scheduler)
  beat:
    build: .
    environment:
      - DATABASE_URL=**********************************/email_management
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-here
      - ENCRYPTION_KEY=your-encryption-key-here
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - .:/app
    command: celery -A app.core.celery_app beat --loglevel=info

volumes:
  postgres_data:
  redis_data:
