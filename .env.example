# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/email_management

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Security
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Gmail API Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:8000/auth/google/callback

# Microsoft Graph API Configuration
MICROSOFT_CLIENT_ID=your-microsoft-client-id
MICROSOFT_CLIENT_SECRET=your-microsoft-client-secret
MICROSOFT_REDIRECT_URI=http://localhost:8000/auth/microsoft/callback

# Application Settings
DEBUG=True
LOG_LEVEL=INFO
MAX_EMAILS_PER_BATCH=100
EMAIL_SYNC_INTERVAL=300

# Encryption
ENCRYPTION_KEY=your-encryption-key-here

# Streamlit Configuration
STREAMLIT_SERVER_PORT=8501
STREAMLIT_SERVER_ADDRESS=localhost
