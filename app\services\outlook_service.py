"""
Microsoft Outlook/Graph API service for email operations.
"""

import base64
from typing import List, Dict, Any, Optional
from datetime import datetime
import aiohttp

from app.models.email import Email, EmailAttachment
from app.models.email_account import EmailAccount


class OutlookService:
    """Microsoft Graph API service for Outlook email operations."""
    
    def __init__(self, email_account: EmailAccount):
        self.email_account = email_account
        self.access_token = email_account.access_token
        self.base_url = "https://graph.microsoft.com/v1.0"
        self.headers = {
            'Authorization': f'Bearer {self.access_token}',
            'Content-Type': 'application/json'
        }
    
    async def get_messages(self, folder: str = "inbox", top: int = 100, 
                          filter_query: str = None) -> List[Dict[str, Any]]:
        """Get messages from Outlook."""
        url = f"{self.base_url}/me/mailFolders/{folder}/messages"
        params = {
            '$top': top,
            '$orderby': 'receivedDateTime desc'
        }
        
        if filter_query:
            params['$filter'] = filter_query
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=self.headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get('value', [])
                    else:
                        print(f"Error fetching messages: {response.status}")
                        return []
        except Exception as e:
            print(f"Error fetching messages: {e}")
            return []
    
    async def get_message_details(self, message_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed message information."""
        url = f"{self.base_url}/me/messages/{message_id}"
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=self.headers) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        print(f"Error fetching message details: {response.status}")
                        return None
        except Exception as e:
            print(f"Error fetching message details: {e}")
            return None
    
    def parse_message(self, message_data: Dict[str, Any]) -> Dict[str, Any]:
        """Parse Outlook message data into standardized format."""
        # Extract sender information
        sender = message_data.get('sender', {})
        sender_email = sender.get('emailAddress', {}).get('address', '')
        sender_name = sender.get('emailAddress', {}).get('name', '')
        
        # Extract recipients
        to_recipients = message_data.get('toRecipients', [])
        cc_recipients = message_data.get('ccRecipients', [])
        bcc_recipients = message_data.get('bccRecipients', [])
        
        to_emails = [r['emailAddress']['address'] for r in to_recipients]
        cc_emails = [r['emailAddress']['address'] for r in cc_recipients]
        bcc_emails = [r['emailAddress']['address'] for r in bcc_recipients]
        
        # Parse date
        received_at = None
        if message_data.get('receivedDateTime'):
            try:
                received_at = datetime.fromisoformat(
                    message_data['receivedDateTime'].replace('Z', '+00:00')
                )
            except:
                received_at = datetime.utcnow()
        
        # Extract attachments info
        attachments = []
        has_attachments = message_data.get('hasAttachments', False)
        
        # Extract categories (labels)
        categories = message_data.get('categories', [])
        
        return {
            'message_id': message_data.get('internetMessageId', ''),
            'thread_id': message_data.get('conversationId', ''),
            'subject': message_data.get('subject', ''),
            'sender_email': sender_email,
            'sender_name': sender_name,
            'recipient_emails': to_emails,
            'cc_emails': cc_emails,
            'bcc_emails': bcc_emails,
            'body_text': message_data.get('body', {}).get('content', '') if message_data.get('body', {}).get('contentType') == 'text' else '',
            'body_html': message_data.get('body', {}).get('content', '') if message_data.get('body', {}).get('contentType') == 'html' else '',
            'received_at': received_at,
            'labels': categories,
            'attachments': attachments,
            'has_attachments': has_attachments,
            'attachment_count': len(attachments),
            'is_read': message_data.get('isRead', False),
            'is_starred': message_data.get('flag', {}).get('flagStatus') == 'flagged',
            'is_important': message_data.get('importance') == 'high',
            'folder_name': message_data.get('parentFolderId', '')
        }
    
    async def get_attachments(self, message_id: str) -> List[Dict[str, Any]]:
        """Get message attachments."""
        url = f"{self.base_url}/me/messages/{message_id}/attachments"
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=self.headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get('value', [])
                    else:
                        print(f"Error fetching attachments: {response.status}")
                        return []
        except Exception as e:
            print(f"Error fetching attachments: {e}")
            return []
    
    async def download_attachment(self, message_id: str, attachment_id: str) -> Optional[bytes]:
        """Download email attachment."""
        url = f"{self.base_url}/me/messages/{message_id}/attachments/{attachment_id}"
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=self.headers) as response:
                    if response.status == 200:
                        attachment_data = await response.json()
                        content_bytes = attachment_data.get('contentBytes', '')
                        if content_bytes:
                            return base64.b64decode(content_bytes)
                    else:
                        print(f"Error downloading attachment: {response.status}")
                        return None
        except Exception as e:
            print(f"Error downloading attachment: {e}")
            return None
    
    async def send_message(self, to_emails: List[str], subject: str, body: str,
                          cc_emails: List[str] = None, bcc_emails: List[str] = None,
                          body_type: str = "text") -> bool:
        """Send an email message."""
        url = f"{self.base_url}/me/sendMail"
        
        # Build recipients
        to_recipients = [{"emailAddress": {"address": email}} for email in to_emails]
        cc_recipients = [{"emailAddress": {"address": email}} for email in (cc_emails or [])]
        bcc_recipients = [{"emailAddress": {"address": email}} for email in (bcc_emails or [])]
        
        message_data = {
            "message": {
                "subject": subject,
                "body": {
                    "contentType": body_type,
                    "content": body
                },
                "toRecipients": to_recipients,
                "ccRecipients": cc_recipients,
                "bccRecipients": bcc_recipients
            }
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=self.headers, json=message_data) as response:
                    return response.status == 202
        except Exception as e:
            print(f"Error sending message: {e}")
            return False
    
    async def mark_as_read(self, message_id: str) -> bool:
        """Mark message as read."""
        url = f"{self.base_url}/me/messages/{message_id}"
        data = {"isRead": True}
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.patch(url, headers=self.headers, json=data) as response:
                    return response.status == 200
        except Exception as e:
            print(f"Error marking message as read: {e}")
            return False
    
    async def move_to_folder(self, message_id: str, folder_id: str) -> bool:
        """Move message to a specific folder."""
        url = f"{self.base_url}/me/messages/{message_id}/move"
        data = {"destinationId": folder_id}
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=self.headers, json=data) as response:
                    return response.status == 201
        except Exception as e:
            print(f"Error moving message: {e}")
            return False
    
    async def get_folders(self) -> List[Dict[str, Any]]:
        """Get all mail folders."""
        url = f"{self.base_url}/me/mailFolders"
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=self.headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get('value', [])
                    else:
                        print(f"Error fetching folders: {response.status}")
                        return []
        except Exception as e:
            print(f"Error fetching folders: {e}")
            return []
