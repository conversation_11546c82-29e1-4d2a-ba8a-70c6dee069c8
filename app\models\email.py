"""
Email model for storing and managing email data.
"""

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>teger, String, Boolean, DateTime, Text, ForeignKey, Enum, JSON, Float
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum

from app.core.database import Base


class EmailStatus(enum.Enum):
    """Email processing status."""
    PENDING = "pending"
    PROCESSING = "processing"
    PROCESSED = "processed"
    ERROR = "error"


class EmailCategory(enum.Enum):
    """Email categories for classification."""
    WORK = "work"
    PERSONAL = "personal"
    NEWSLETTER = "newsletter"
    SPAM = "spam"
    URGENT = "urgent"
    SOCIAL = "social"
    PROMOTIONAL = "promotional"
    FINANCE = "finance"
    TRAVEL = "travel"
    OTHER = "other"


class EmailSentiment(enum.Enum):
    """Email sentiment analysis results."""
    POSITIVE = "positive"
    NEGATIVE = "negative"
    NEUTRAL = "neutral"
    URGENT = "urgent"


class Email(Base):
    """Email model for storing email data and metadata."""

    __tablename__ = "emails"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    email_account_id = Column(Integer, ForeignKey("email_accounts.id"), nullable=False)

    # Email identifiers
    message_id = Column(String(255), nullable=False, index=True)
    thread_id = Column(String(255), nullable=True, index=True)

    # Email headers
    subject = Column(Text, nullable=True)
    sender_email = Column(String(255), nullable=False, index=True)
    sender_name = Column(String(255), nullable=True)
    recipient_emails = Column(JSON, nullable=True)  # List of recipient emails
    cc_emails = Column(JSON, nullable=True)  # List of CC emails
    bcc_emails = Column(JSON, nullable=True)  # List of BCC emails

    # Email content
    body_text = Column(Text, nullable=True)
    body_html = Column(Text, nullable=True)

    # Email metadata
    received_at = Column(DateTime(timezone=True), nullable=False, index=True)
    sent_at = Column(DateTime(timezone=True), nullable=True)
    is_read = Column(Boolean, default=False)
    is_starred = Column(Boolean, default=False)
    is_important = Column(Boolean, default=False)

    # Folder/label information
    folder_name = Column(String(255), nullable=True)
    labels = Column(JSON, nullable=True)  # List of labels/tags

    # Processing status
    status = Column(Enum(EmailStatus), default=EmailStatus.PENDING)
    processed_at = Column(DateTime(timezone=True), nullable=True)

    # AI Classification results
    category = Column(Enum(EmailCategory), nullable=True)
    category_confidence = Column(Float, nullable=True)
    sentiment = Column(Enum(EmailSentiment), nullable=True)
    sentiment_score = Column(Float, nullable=True)

    # Extracted entities (JSON format)
    entities = Column(JSON, nullable=True)  # People, organizations, dates, etc.

    # Language detection
    language = Column(String(10), nullable=True)

    # Attachments
    has_attachments = Column(Boolean, default=False)
    attachment_count = Column(Integer, default=0)

    # Response tracking
    requires_response = Column(Boolean, default=False)
    response_deadline = Column(DateTime(timezone=True), nullable=True)
    is_responded = Column(Boolean, default=False)
    responded_at = Column(DateTime(timezone=True), nullable=True)

    # Duplicate detection
    is_duplicate = Column(Boolean, default=False)
    duplicate_of_id = Column(Integer, ForeignKey("emails.id"), nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="emails")
    email_account = relationship("EmailAccount", back_populates="emails")
    attachments = relationship("EmailAttachment", back_populates="email", cascade="all, delete-orphan")
    smart_replies = relationship("SmartReply", back_populates="email", cascade="all, delete-orphan")

    # Self-referential relationship for duplicates
    duplicate_of = relationship("Email", remote_side=[id])

    def __repr__(self):
        return f"<Email(id={self.id}, subject='{self.subject[:50] if self.subject else 'No Subject'}...', sender='{self.sender_email}')>"


class EmailAttachment(Base):
    """Email attachment model."""

    __tablename__ = "email_attachments"

    id = Column(Integer, primary_key=True, index=True)
    email_id = Column(Integer, ForeignKey("emails.id"), nullable=False)

    # Attachment details
    filename = Column(String(255), nullable=False)
    content_type = Column(String(100), nullable=True)
    size_bytes = Column(Integer, nullable=True)

    # File storage
    file_path = Column(String(500), nullable=True)  # Local file path or cloud storage URL
    is_inline = Column(Boolean, default=False)
    content_id = Column(String(255), nullable=True)  # For inline attachments

    # Analysis results
    file_type = Column(String(50), nullable=True)
    is_safe = Column(Boolean, default=True)
    scan_results = Column(JSON, nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    email = relationship("Email", back_populates="attachments")

    def __repr__(self):
        return f"<EmailAttachment(id={self.id}, filename='{self.filename}', size={self.size_bytes})>"
