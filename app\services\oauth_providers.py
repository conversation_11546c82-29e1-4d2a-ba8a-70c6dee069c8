"""
OAuth providers for email service authentication.
"""

import json
import base64
from typing import Dict, Any, Optional
from urllib.parse import urlencode
import aiohttp
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import Flow
import msal

from app.core.config import settings


class GoogleOAuth:
    """Google OAuth 2.0 authentication handler."""
    
    def __init__(self):
        self.client_id = settings.GOOGLE_CLIENT_ID
        self.client_secret = settings.GOOGLE_CLIENT_SECRET
        self.redirect_uri = settings.GOOGLE_REDIRECT_URI
        self.scopes = [
            'https://www.googleapis.com/auth/gmail.readonly',
            'https://www.googleapis.com/auth/gmail.send',
            'https://www.googleapis.com/auth/gmail.modify',
            'https://www.googleapis.com/auth/userinfo.email',
            'https://www.googleapis.com/auth/userinfo.profile'
        ]
    
    def get_authorization_url(self) -> str:
        """Get the authorization URL for Google OAuth."""
        flow = Flow.from_client_config(
            {
                "web": {
                    "client_id": self.client_id,
                    "client_secret": self.client_secret,
                    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                    "token_uri": "https://oauth2.googleapis.com/token",
                    "redirect_uris": [self.redirect_uri]
                }
            },
            scopes=self.scopes
        )
        flow.redirect_uri = self.redirect_uri
        
        authorization_url, _ = flow.authorization_url(
            access_type='offline',
            include_granted_scopes='true'
        )
        
        return authorization_url
    
    async def get_user_info(self, authorization_code: str) -> Dict[str, Any]:
        """Exchange authorization code for user information."""
        flow = Flow.from_client_config(
            {
                "web": {
                    "client_id": self.client_id,
                    "client_secret": self.client_secret,
                    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                    "token_uri": "https://oauth2.googleapis.com/token",
                    "redirect_uris": [self.redirect_uri]
                }
            },
            scopes=self.scopes
        )
        flow.redirect_uri = self.redirect_uri
        
        # Exchange code for tokens
        flow.fetch_token(code=authorization_code)
        credentials = flow.credentials
        
        # Get user info
        async with aiohttp.ClientSession() as session:
            headers = {'Authorization': f'Bearer {credentials.token}'}
            async with session.get(
                'https://www.googleapis.com/oauth2/v2/userinfo',
                headers=headers
            ) as response:
                user_info = await response.json()
        
        # Add token information
        user_info['access_token'] = credentials.token
        user_info['refresh_token'] = credentials.refresh_token
        user_info['token_expires_at'] = credentials.expiry
        
        return user_info
    
    async def refresh_token(self, refresh_token: str) -> Dict[str, Any]:
        """Refresh an expired access token."""
        credentials = Credentials(
            token=None,
            refresh_token=refresh_token,
            token_uri="https://oauth2.googleapis.com/token",
            client_id=self.client_id,
            client_secret=self.client_secret
        )
        
        credentials.refresh(Request())
        
        return {
            'access_token': credentials.token,
            'refresh_token': credentials.refresh_token,
            'expires_at': credentials.expiry
        }


class MicrosoftOAuth:
    """Microsoft OAuth 2.0 authentication handler."""
    
    def __init__(self):
        self.client_id = settings.MICROSOFT_CLIENT_ID
        self.client_secret = settings.MICROSOFT_CLIENT_SECRET
        self.redirect_uri = settings.MICROSOFT_REDIRECT_URI
        self.authority = "https://login.microsoftonline.com/common"
        self.scopes = [
            "https://graph.microsoft.com/Mail.Read",
            "https://graph.microsoft.com/Mail.Send",
            "https://graph.microsoft.com/Mail.ReadWrite",
            "https://graph.microsoft.com/User.Read"
        ]
    
    def get_authorization_url(self) -> str:
        """Get the authorization URL for Microsoft OAuth."""
        app = msal.ConfidentialClientApplication(
            self.client_id,
            authority=self.authority,
            client_credential=self.client_secret
        )
        
        auth_url = app.get_authorization_request_url(
            scopes=self.scopes,
            redirect_uri=self.redirect_uri
        )
        
        return auth_url
    
    async def get_user_info(self, authorization_code: str) -> Dict[str, Any]:
        """Exchange authorization code for user information."""
        app = msal.ConfidentialClientApplication(
            self.client_id,
            authority=self.authority,
            client_credential=self.client_secret
        )
        
        # Exchange code for tokens
        result = app.acquire_token_by_authorization_code(
            authorization_code,
            scopes=self.scopes,
            redirect_uri=self.redirect_uri
        )
        
        if "error" in result:
            raise Exception(f"OAuth error: {result.get('error_description', result['error'])}")
        
        # Get user info from Microsoft Graph
        access_token = result['access_token']
        async with aiohttp.ClientSession() as session:
            headers = {'Authorization': f'Bearer {access_token}'}
            async with session.get(
                'https://graph.microsoft.com/v1.0/me',
                headers=headers
            ) as response:
                user_info = await response.json()
        
        # Add token information
        user_info['access_token'] = access_token
        user_info['refresh_token'] = result.get('refresh_token')
        user_info['token_expires_at'] = result.get('expires_in')
        
        return user_info
    
    async def refresh_token(self, refresh_token: str) -> Dict[str, Any]:
        """Refresh an expired access token."""
        app = msal.ConfidentialClientApplication(
            self.client_id,
            authority=self.authority,
            client_credential=self.client_secret
        )
        
        result = app.acquire_token_by_refresh_token(
            refresh_token,
            scopes=self.scopes
        )
        
        if "error" in result:
            raise Exception(f"Token refresh error: {result.get('error_description', result['error'])}")
        
        return {
            'access_token': result['access_token'],
            'refresh_token': result.get('refresh_token', refresh_token),
            'expires_at': result.get('expires_in')
        }
