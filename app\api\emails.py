"""
Email API endpoints.
"""

from typing import List, Optional
from fastapi import API<PERSON>outer, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.models.email import Email, EmailCategory, EmailSentiment
from app.models.email_account import EmailAccount
from app.services.email_service import EmailAccountManager
from app.schemas.email import EmailResponse, EmailListResponse, EmailSyncRequest

router = APIRouter()


@router.get("/", response_model=EmailListResponse)
async def get_emails(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    category: Optional[EmailCategory] = None,
    is_read: Optional[bool] = None,
    is_starred: Optional[bool] = None,
    sender_email: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get user's emails with filtering options."""
    query = db.query(Email).filter(Email.user_id == current_user.id)
    
    # Apply filters
    if category:
        query = query.filter(Email.category == category)
    if is_read is not None:
        query = query.filter(Email.is_read == is_read)
    if is_starred is not None:
        query = query.filter(Email.is_starred == is_starred)
    if sender_email:
        query = query.filter(Email.sender_email.ilike(f"%{sender_email}%"))
    
    # Get total count
    total = query.count()
    
    # Apply pagination and ordering
    emails = query.order_by(Email.received_at.desc()).offset(skip).limit(limit).all()
    
    return EmailListResponse(
        emails=emails,
        total=total,
        skip=skip,
        limit=limit
    )


@router.get("/{email_id}", response_model=EmailResponse)
async def get_email(
    email_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get a specific email by ID."""
    email = db.query(Email).filter(
        Email.id == email_id,
        Email.user_id == current_user.id
    ).first()
    
    if not email:
        raise HTTPException(status_code=404, detail="Email not found")
    
    return email


@router.post("/sync")
async def sync_emails(
    sync_request: EmailSyncRequest = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Sync emails from all connected accounts."""
    try:
        results = await EmailAccountManager.sync_all_accounts(db, current_user.id)
        return {
            "message": "Email sync completed",
            "results": results
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Sync failed: {str(e)}")


@router.patch("/{email_id}/read")
async def mark_email_read(
    email_id: int,
    is_read: bool = True,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Mark an email as read or unread."""
    email = db.query(Email).filter(
        Email.id == email_id,
        Email.user_id == current_user.id
    ).first()
    
    if not email:
        raise HTTPException(status_code=404, detail="Email not found")
    
    email.is_read = is_read
    db.commit()
    
    # Also mark as read in the email provider
    email_account = db.query(EmailAccount).filter(
        EmailAccount.id == email.email_account_id
    ).first()
    
    if email_account:
        try:
            email_service = EmailAccountManager.get_email_service(email_account)
            await email_service.mark_as_read(email.message_id)
        except Exception as e:
            print(f"Failed to mark email as read in provider: {e}")
    
    return {"message": f"Email marked as {'read' if is_read else 'unread'}"}


@router.patch("/{email_id}/star")
async def star_email(
    email_id: int,
    is_starred: bool = True,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Star or unstar an email."""
    email = db.query(Email).filter(
        Email.id == email_id,
        Email.user_id == current_user.id
    ).first()
    
    if not email:
        raise HTTPException(status_code=404, detail="Email not found")
    
    email.is_starred = is_starred
    db.commit()
    
    return {"message": f"Email {'starred' if is_starred else 'unstarred'}"}


@router.patch("/{email_id}/category")
async def categorize_email(
    email_id: int,
    category: EmailCategory,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Categorize an email."""
    email = db.query(Email).filter(
        Email.id == email_id,
        Email.user_id == current_user.id
    ).first()
    
    if not email:
        raise HTTPException(status_code=404, detail="Email not found")
    
    email.category = category
    db.commit()
    
    return {"message": f"Email categorized as {category.value}"}


@router.delete("/{email_id}")
async def delete_email(
    email_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Delete an email."""
    email = db.query(Email).filter(
        Email.id == email_id,
        Email.user_id == current_user.id
    ).first()
    
    if not email:
        raise HTTPException(status_code=404, detail="Email not found")
    
    db.delete(email)
    db.commit()
    
    return {"message": "Email deleted"}


@router.get("/search/")
async def search_emails(
    q: str = Query(..., min_length=1),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Search emails by content."""
    query = db.query(Email).filter(
        Email.user_id == current_user.id
    ).filter(
        Email.subject.ilike(f"%{q}%") |
        Email.body_text.ilike(f"%{q}%") |
        Email.sender_email.ilike(f"%{q}%") |
        Email.sender_name.ilike(f"%{q}%")
    )
    
    total = query.count()
    emails = query.order_by(Email.received_at.desc()).offset(skip).limit(limit).all()
    
    return EmailListResponse(
        emails=emails,
        total=total,
        skip=skip,
        limit=limit
    )


@router.get("/stats/summary")
async def get_email_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get email statistics summary."""
    total_emails = db.query(Email).filter(Email.user_id == current_user.id).count()
    unread_emails = db.query(Email).filter(
        Email.user_id == current_user.id,
        Email.is_read == False
    ).count()
    starred_emails = db.query(Email).filter(
        Email.user_id == current_user.id,
        Email.is_starred == True
    ).count()
    
    # Category distribution
    from sqlalchemy import func
    category_stats = db.query(
        Email.category,
        func.count(Email.id).label('count')
    ).filter(
        Email.user_id == current_user.id,
        Email.category.isnot(None)
    ).group_by(Email.category).all()
    
    category_distribution = {stat.category.value: stat.count for stat in category_stats}
    
    return {
        "total_emails": total_emails,
        "unread_emails": unread_emails,
        "starred_emails": starred_emails,
        "read_rate": (total_emails - unread_emails) / total_emails * 100 if total_emails > 0 else 0,
        "category_distribution": category_distribution
    }
