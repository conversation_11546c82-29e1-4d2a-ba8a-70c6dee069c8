"""
Celery configuration for background tasks.
"""

from celery import Celery
from app.core.config import settings

# Create Celery instance
celery_app = Celery(
    "email_management",
    broker=settings.REDIS_URL,
    backend=settings.REDIS_URL,
    include=[
        "app.services.email_processor",
        "app.services.ml_classifier",
        "app.services.analytics_processor"
    ]
)

# Celery configuration
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
)

# Periodic tasks
celery_app.conf.beat_schedule = {
    "sync-emails": {
        "task": "app.services.email_processor.sync_all_accounts",
        "schedule": settings.EMAIL_SYNC_INTERVAL,
    },
    "process-pending-emails": {
        "task": "app.services.email_processor.process_pending_emails",
        "schedule": 60.0,  # Every minute
    },
    "update-analytics": {
        "task": "app.services.analytics_processor.update_daily_analytics",
        "schedule": 3600.0,  # Every hour
    },
    "cleanup-old-data": {
        "task": "app.services.email_processor.cleanup_old_data",
        "schedule": 86400.0,  # Daily
    },
}
