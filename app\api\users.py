"""
User management API endpoints.
"""

from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.models.email_account import EmailAccount, EmailProvider
from app.schemas.user import UserResponse, UserUpdate, EmailAccountCreate, EmailAccountResponse
from app.services.email_service import EmailAccountManager

router = APIRouter()


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """Get current user information."""
    return current_user


@router.patch("/me", response_model=UserResponse)
async def update_user_profile(
    user_update: UserUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update user profile."""
    for field, value in user_update.dict(exclude_unset=True).items():
        setattr(current_user, field, value)
    
    db.commit()
    db.refresh(current_user)
    return current_user


@router.get("/email-accounts", response_model=List[EmailAccountResponse])
async def get_email_accounts(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get user's email accounts."""
    accounts = db.query(EmailAccount).filter(
        EmailAccount.user_id == current_user.id
    ).all()
    return accounts


@router.post("/email-accounts", response_model=EmailAccountResponse)
async def create_email_account(
    account_data: EmailAccountCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new email account."""
    # Check if account already exists
    existing_account = db.query(EmailAccount).filter(
        EmailAccount.user_id == current_user.id,
        EmailAccount.email_address == account_data.email_address
    ).first()
    
    if existing_account:
        raise HTTPException(
            status_code=400,
            detail="Email account already exists"
        )
    
    # Create new account
    account = EmailAccount(
        user_id=current_user.id,
        **account_data.dict()
    )
    
    db.add(account)
    db.commit()
    db.refresh(account)
    
    return account


@router.get("/email-accounts/{account_id}", response_model=EmailAccountResponse)
async def get_email_account(
    account_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get a specific email account."""
    account = db.query(EmailAccount).filter(
        EmailAccount.id == account_id,
        EmailAccount.user_id == current_user.id
    ).first()
    
    if not account:
        raise HTTPException(status_code=404, detail="Email account not found")
    
    return account


@router.patch("/email-accounts/{account_id}")
async def update_email_account(
    account_id: int,
    is_active: bool = None,
    sync_enabled: bool = None,
    sync_frequency: int = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update email account settings."""
    account = db.query(EmailAccount).filter(
        EmailAccount.id == account_id,
        EmailAccount.user_id == current_user.id
    ).first()
    
    if not account:
        raise HTTPException(status_code=404, detail="Email account not found")
    
    if is_active is not None:
        account.is_active = is_active
    if sync_enabled is not None:
        account.sync_enabled = sync_enabled
    if sync_frequency is not None:
        account.sync_frequency = sync_frequency
    
    db.commit()
    
    return {"message": "Email account updated successfully"}


@router.delete("/email-accounts/{account_id}")
async def delete_email_account(
    account_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Delete an email account."""
    account = db.query(EmailAccount).filter(
        EmailAccount.id == account_id,
        EmailAccount.user_id == current_user.id
    ).first()
    
    if not account:
        raise HTTPException(status_code=404, detail="Email account not found")
    
    db.delete(account)
    db.commit()
    
    return {"message": "Email account deleted successfully"}


@router.post("/email-accounts/{account_id}/test-connection")
async def test_email_connection(
    account_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Test connection to an email account."""
    account = db.query(EmailAccount).filter(
        EmailAccount.id == account_id,
        EmailAccount.user_id == current_user.id
    ).first()
    
    if not account:
        raise HTTPException(status_code=404, detail="Email account not found")
    
    try:
        result = await EmailAccountManager.test_connection(account)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Connection test failed: {str(e)}"
        )
