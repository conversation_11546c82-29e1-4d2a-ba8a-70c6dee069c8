"""
Email schemas for API requests and responses.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, EmailStr

from app.models.email import EmailCategory, EmailSentiment, EmailStatus


class EmailAttachmentResponse(BaseModel):
    """Email attachment response schema."""
    id: int
    filename: str
    content_type: Optional[str]
    size_bytes: Optional[int]
    is_inline: bool
    
    class Config:
        from_attributes = True


class EmailResponse(BaseModel):
    """Email response schema."""
    id: int
    message_id: str
    thread_id: Optional[str]
    subject: Optional[str]
    sender_email: str
    sender_name: Optional[str]
    recipient_emails: Optional[List[str]]
    cc_emails: Optional[List[str]]
    bcc_emails: Optional[List[str]]
    body_text: Optional[str]
    body_html: Optional[str]
    received_at: Optional[datetime]
    is_read: bool
    is_starred: bool
    is_important: bool
    folder_name: Optional[str]
    labels: Optional[List[str]]
    category: Optional[EmailCategory]
    category_confidence: Optional[float]
    sentiment: Optional[EmailSentiment]
    sentiment_score: Optional[float]
    has_attachments: bool
    attachment_count: int
    attachments: List[EmailAttachmentResponse] = []
    requires_response: bool
    response_deadline: Optional[datetime]
    is_responded: bool
    language: Optional[str]
    entities: Optional[Dict[str, Any]]
    status: EmailStatus
    created_at: datetime
    
    class Config:
        from_attributes = True


class EmailListResponse(BaseModel):
    """Email list response schema."""
    emails: List[EmailResponse]
    total: int
    skip: int
    limit: int


class EmailSyncRequest(BaseModel):
    """Email sync request schema."""
    account_ids: Optional[List[int]] = None
    force_full_sync: bool = False


class EmailSendRequest(BaseModel):
    """Email send request schema."""
    to_emails: List[EmailStr]
    subject: str
    body: str
    cc_emails: Optional[List[EmailStr]] = None
    bcc_emails: Optional[List[EmailStr]] = None
    body_type: str = "text"  # "text" or "html"


class EmailSearchRequest(BaseModel):
    """Email search request schema."""
    query: str
    category: Optional[EmailCategory] = None
    sender_email: Optional[str] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    has_attachments: Optional[bool] = None
    is_read: Optional[bool] = None
    is_starred: Optional[bool] = None
