"""
IMAP service for generic email provider support.
"""

import email
import imaplib
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMult<PERSON>art
from typing import List, Dict, Any, Optional
from datetime import datetime
import ssl

from app.models.email_account import EmailAccount


class IMAPService:
    """IMAP service for generic email providers."""
    
    def __init__(self, email_account: EmailAccount):
        self.email_account = email_account
        self.imap_server = email_account.imap_server
        self.imap_port = email_account.imap_port or (993 if email_account.imap_use_ssl else 143)
        self.smtp_server = email_account.smtp_server
        self.smtp_port = email_account.smtp_port or (587 if email_account.smtp_use_ssl else 25)
        self.use_ssl = email_account.imap_use_ssl
        self.email_address = email_account.email_address
        # Note: For IMAP, we'd typically store the password encrypted
        # This is a simplified implementation
        self.password = email_account.access_token  # In real implementation, this would be decrypted
        
        self.imap_connection = None
        self.smtp_connection = None
    
    def connect_imap(self) -> bool:
        """Connect to IMAP server."""
        try:
            if self.use_ssl:
                self.imap_connection = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
            else:
                self.imap_connection = imaplib.IMAP4(self.imap_server, self.imap_port)
                if self.imap_port == 143:  # Standard IMAP port, try STARTTLS
                    self.imap_connection.starttls()
            
            self.imap_connection.login(self.email_address, self.password)
            return True
        except Exception as e:
            print(f"IMAP connection error: {e}")
            return False
    
    def connect_smtp(self) -> bool:
        """Connect to SMTP server."""
        try:
            if self.smtp_port == 465:  # SSL
                self.smtp_connection = smtplib.SMTP_SSL(self.smtp_server, self.smtp_port)
            else:
                self.smtp_connection = smtplib.SMTP(self.smtp_server, self.smtp_port)
                if self.smtp_port == 587:  # TLS
                    self.smtp_connection.starttls()
            
            self.smtp_connection.login(self.email_address, self.password)
            return True
        except Exception as e:
            print(f"SMTP connection error: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from servers."""
        if self.imap_connection:
            try:
                self.imap_connection.close()
                self.imap_connection.logout()
            except:
                pass
            self.imap_connection = None
        
        if self.smtp_connection:
            try:
                self.smtp_connection.quit()
            except:
                pass
            self.smtp_connection = None
    
    async def get_messages(self, folder: str = "INBOX", limit: int = 100) -> List[Dict[str, Any]]:
        """Get messages from IMAP server."""
        if not self.connect_imap():
            return []
        
        try:
            # Select folder
            self.imap_connection.select(folder)
            
            # Search for messages
            status, message_ids = self.imap_connection.search(None, 'ALL')
            if status != 'OK':
                return []
            
            message_ids = message_ids[0].split()
            # Get the most recent messages
            message_ids = message_ids[-limit:] if len(message_ids) > limit else message_ids
            
            messages = []
            for msg_id in reversed(message_ids):  # Most recent first
                try:
                    status, msg_data = self.imap_connection.fetch(msg_id, '(RFC822)')
                    if status == 'OK':
                        email_message = email.message_from_bytes(msg_data[0][1])
                        parsed_message = self.parse_message(email_message, msg_id.decode())
                        messages.append(parsed_message)
                except Exception as e:
                    print(f"Error parsing message {msg_id}: {e}")
                    continue
            
            return messages
        except Exception as e:
            print(f"Error fetching messages: {e}")
            return []
        finally:
            self.disconnect()
    
    def parse_message(self, email_message: email.message.EmailMessage, message_id: str) -> Dict[str, Any]:
        """Parse email message into standardized format."""
        # Extract headers
        subject = email_message.get('Subject', '')
        sender = email_message.get('From', '')
        to_recipients = email_message.get('To', '')
        cc_recipients = email_message.get('Cc', '')
        bcc_recipients = email_message.get('Bcc', '')
        date_str = email_message.get('Date', '')
        
        # Parse sender
        sender_email = sender
        sender_name = ""
        if '<' in sender and '>' in sender:
            sender_name = sender.split('<')[0].strip().strip('"')
            sender_email = sender.split('<')[1].split('>')[0].strip()
        
        # Parse recipients
        to_emails = [addr.strip() for addr in to_recipients.split(',')] if to_recipients else []
        cc_emails = [addr.strip() for addr in cc_recipients.split(',')] if cc_recipients else []
        bcc_emails = [addr.strip() for addr in bcc_recipients.split(',')] if bcc_recipients else []
        
        # Parse date
        received_at = None
        if date_str:
            try:
                received_at = email.utils.parsedate_to_datetime(date_str)
            except:
                received_at = datetime.utcnow()
        
        # Extract body and attachments
        body_text = ""
        body_html = ""
        attachments = []
        
        if email_message.is_multipart():
            for part in email_message.walk():
                content_type = part.get_content_type()
                content_disposition = str(part.get("Content-Disposition", ""))
                
                if "attachment" in content_disposition:
                    # This is an attachment
                    filename = part.get_filename()
                    if filename:
                        attachments.append({
                            'filename': filename,
                            'content_type': content_type,
                            'size': len(part.get_payload(decode=True) or b''),
                            'content': part.get_payload(decode=True)
                        })
                elif content_type == "text/plain" and "attachment" not in content_disposition:
                    body_text = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                elif content_type == "text/html" and "attachment" not in content_disposition:
                    body_html = part.get_payload(decode=True).decode('utf-8', errors='ignore')
        else:
            # Single part message
            content_type = email_message.get_content_type()
            if content_type == "text/plain":
                body_text = email_message.get_payload(decode=True).decode('utf-8', errors='ignore')
            elif content_type == "text/html":
                body_html = email_message.get_payload(decode=True).decode('utf-8', errors='ignore')
        
        return {
            'message_id': email_message.get('Message-ID', message_id),
            'thread_id': email_message.get('In-Reply-To', ''),
            'subject': subject,
            'sender_email': sender_email,
            'sender_name': sender_name,
            'recipient_emails': to_emails,
            'cc_emails': cc_emails,
            'bcc_emails': bcc_emails,
            'body_text': body_text,
            'body_html': body_html,
            'received_at': received_at,
            'labels': [],  # IMAP doesn't have labels like Gmail
            'attachments': attachments,
            'has_attachments': len(attachments) > 0,
            'attachment_count': len(attachments),
            'is_read': True,  # Would need to check IMAP flags
            'is_starred': False,  # Would need to check IMAP flags
            'is_important': False,  # Would need to check IMAP flags
            'folder_name': 'INBOX'  # Current folder
        }
    
    async def send_message(self, to_emails: List[str], subject: str, body: str,
                          cc_emails: List[str] = None, bcc_emails: List[str] = None,
                          body_type: str = "text") -> bool:
        """Send an email message via SMTP."""
        if not self.connect_smtp():
            return False
        
        try:
            # Create message
            if body_type == "html":
                msg = MIMEMultipart('alternative')
                msg.attach(MIMEText(body, 'html'))
            else:
                msg = MIMEText(body, 'plain')
            
            msg['Subject'] = subject
            msg['From'] = self.email_address
            msg['To'] = ', '.join(to_emails)
            
            if cc_emails:
                msg['Cc'] = ', '.join(cc_emails)
            
            # Send message
            all_recipients = to_emails + (cc_emails or []) + (bcc_emails or [])
            self.smtp_connection.send_message(msg, to_addrs=all_recipients)
            
            return True
        except Exception as e:
            print(f"Error sending message: {e}")
            return False
        finally:
            self.disconnect()
    
    async def mark_as_read(self, message_id: str) -> bool:
        """Mark message as read (IMAP flags)."""
        if not self.connect_imap():
            return False
        
        try:
            self.imap_connection.select('INBOX')
            self.imap_connection.store(message_id, '+FLAGS', '\\Seen')
            return True
        except Exception as e:
            print(f"Error marking message as read: {e}")
            return False
        finally:
            self.disconnect()
    
    async def move_to_folder(self, message_id: str, folder: str) -> bool:
        """Move message to a different folder."""
        if not self.connect_imap():
            return False
        
        try:
            self.imap_connection.select('INBOX')
            self.imap_connection.move(message_id, folder)
            return True
        except Exception as e:
            print(f"Error moving message: {e}")
            return False
        finally:
            self.disconnect()
    
    async def get_folders(self) -> List[str]:
        """Get list of available folders."""
        if not self.connect_imap():
            return []
        
        try:
            status, folders = self.imap_connection.list()
            if status == 'OK':
                folder_names = []
                for folder in folders:
                    # Parse folder name from IMAP response
                    folder_name = folder.decode().split('"')[-2]
                    folder_names.append(folder_name)
                return folder_names
            return []
        except Exception as e:
            print(f"Error fetching folders: {e}")
            return []
        finally:
            self.disconnect()
