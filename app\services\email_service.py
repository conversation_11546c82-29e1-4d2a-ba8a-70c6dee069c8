"""
Unified email service that works with all email providers.
"""

from typing import List, Dict, Any, Optional, Union
from sqlalchemy.orm import Session

from app.models.email_account import EmailAccount, EmailProvider
from app.models.email import Email, EmailAttachment, EmailStatus
from app.services.gmail_service import GmailService
from app.services.outlook_service import OutlookService
from app.services.imap_service import IMAPService
from app.core.database import get_db


class EmailService:
    """Unified email service for all providers."""
    
    def __init__(self, email_account: EmailAccount):
        self.email_account = email_account
        self.provider_service = self._get_provider_service()
    
    def _get_provider_service(self) -> Union[GmailService, OutlookService, IMAPService]:
        """Get the appropriate provider service."""
        if self.email_account.provider == EmailProvider.GMAIL:
            return GmailService(self.email_account)
        elif self.email_account.provider == EmailProvider.OUTLOOK:
            return OutlookService(self.email_account)
        elif self.email_account.provider == EmailProvider.IMAP:
            return IMAPService(self.email_account)
        else:
            raise ValueError(f"Unsupported email provider: {self.email_account.provider}")
    
    async def sync_emails(self, db: Session, limit: int = 100) -> List[Email]:
        """Sync emails from the provider and save to database."""
        try:
            # Get messages from provider
            if self.email_account.provider == EmailProvider.GMAIL:
                messages = await self.provider_service.get_messages(max_results=limit)
                email_data_list = []
                
                for message in messages:
                    message_details = await self.provider_service.get_message_details(message['id'])
                    if message_details:
                        parsed_data = self.provider_service.parse_message(message_details)
                        email_data_list.append(parsed_data)
            
            elif self.email_account.provider == EmailProvider.OUTLOOK:
                messages = await self.provider_service.get_messages(top=limit)
                email_data_list = []
                
                for message in messages:
                    parsed_data = self.provider_service.parse_message(message)
                    email_data_list.append(parsed_data)
            
            elif self.email_account.provider == EmailProvider.IMAP:
                email_data_list = await self.provider_service.get_messages(limit=limit)
            
            else:
                return []
            
            # Save emails to database
            saved_emails = []
            for email_data in email_data_list:
                # Check if email already exists
                existing_email = db.query(Email).filter(
                    Email.message_id == email_data['message_id'],
                    Email.email_account_id == self.email_account.id
                ).first()
                
                if not existing_email:
                    # Create new email record
                    email_record = Email(
                        user_id=self.email_account.user_id,
                        email_account_id=self.email_account.id,
                        message_id=email_data['message_id'],
                        thread_id=email_data['thread_id'],
                        subject=email_data['subject'],
                        sender_email=email_data['sender_email'],
                        sender_name=email_data['sender_name'],
                        recipient_emails=email_data['recipient_emails'],
                        cc_emails=email_data['cc_emails'],
                        bcc_emails=email_data['bcc_emails'],
                        body_text=email_data['body_text'],
                        body_html=email_data['body_html'],
                        received_at=email_data['received_at'],
                        labels=email_data['labels'],
                        has_attachments=email_data['has_attachments'],
                        attachment_count=email_data['attachment_count'],
                        is_read=email_data['is_read'],
                        is_starred=email_data['is_starred'],
                        is_important=email_data['is_important'],
                        folder_name=email_data['folder_name'],
                        status=EmailStatus.PENDING
                    )
                    
                    db.add(email_record)
                    db.commit()
                    db.refresh(email_record)
                    
                    # Save attachments if any
                    if email_data['attachments']:
                        await self._save_attachments(db, email_record, email_data['attachments'])
                    
                    saved_emails.append(email_record)
            
            return saved_emails
            
        except Exception as e:
            print(f"Error syncing emails: {e}")
            return []
    
    async def _save_attachments(self, db: Session, email_record: Email, 
                               attachments_data: List[Dict[str, Any]]):
        """Save email attachments to database."""
        for attachment_data in attachments_data:
            attachment = EmailAttachment(
                email_id=email_record.id,
                filename=attachment_data['filename'],
                content_type=attachment_data.get('content_type', ''),
                size_bytes=attachment_data.get('size', 0),
                is_inline=attachment_data.get('is_inline', False),
                content_id=attachment_data.get('content_id', '')
            )
            
            db.add(attachment)
        
        db.commit()
    
    async def send_email(self, to_emails: List[str], subject: str, body: str,
                        cc_emails: List[str] = None, bcc_emails: List[str] = None,
                        body_type: str = "text") -> bool:
        """Send an email through the provider."""
        try:
            return await self.provider_service.send_message(
                to_emails=to_emails,
                subject=subject,
                body=body,
                cc_emails=cc_emails,
                bcc_emails=bcc_emails
            )
        except Exception as e:
            print(f"Error sending email: {e}")
            return False
    
    async def mark_as_read(self, message_id: str) -> bool:
        """Mark an email as read."""
        try:
            return await self.provider_service.mark_as_read(message_id)
        except Exception as e:
            print(f"Error marking email as read: {e}")
            return False
    
    async def get_folders(self) -> List[Dict[str, Any]]:
        """Get available folders/labels."""
        try:
            if self.email_account.provider == EmailProvider.GMAIL:
                return await self.provider_service.get_labels()
            elif self.email_account.provider == EmailProvider.OUTLOOK:
                return await self.provider_service.get_folders()
            elif self.email_account.provider == EmailProvider.IMAP:
                folders = await self.provider_service.get_folders()
                return [{"name": folder, "id": folder} for folder in folders]
            else:
                return []
        except Exception as e:
            print(f"Error getting folders: {e}")
            return []
    
    async def download_attachment(self, message_id: str, attachment_id: str) -> Optional[bytes]:
        """Download an email attachment."""
        try:
            return await self.provider_service.download_attachment(message_id, attachment_id)
        except Exception as e:
            print(f"Error downloading attachment: {e}")
            return None


class EmailAccountManager:
    """Manager for email accounts and their services."""
    
    @staticmethod
    def get_email_service(email_account: EmailAccount) -> EmailService:
        """Get email service for an account."""
        return EmailService(email_account)
    
    @staticmethod
    async def sync_all_accounts(db: Session, user_id: int) -> Dict[str, Any]:
        """Sync emails for all user accounts."""
        email_accounts = db.query(EmailAccount).filter(
            EmailAccount.user_id == user_id,
            EmailAccount.is_active == True,
            EmailAccount.sync_enabled == True
        ).all()
        
        results = {
            'total_accounts': len(email_accounts),
            'synced_accounts': 0,
            'total_emails': 0,
            'errors': []
        }
        
        for account in email_accounts:
            try:
                email_service = EmailService(account)
                synced_emails = await email_service.sync_emails(db)
                
                results['synced_accounts'] += 1
                results['total_emails'] += len(synced_emails)
                
                # Update last sync time
                from datetime import datetime
                account.last_sync_at = datetime.utcnow()
                db.commit()
                
            except Exception as e:
                error_msg = f"Error syncing account {account.email_address}: {str(e)}"
                results['errors'].append(error_msg)
                print(error_msg)
        
        return results
    
    @staticmethod
    async def test_connection(email_account: EmailAccount) -> Dict[str, Any]:
        """Test connection to an email account."""
        try:
            email_service = EmailService(email_account)
            
            # Try to get a small number of messages to test connection
            if email_account.provider == EmailProvider.GMAIL:
                messages = await email_service.provider_service.get_messages(max_results=1)
            elif email_account.provider == EmailProvider.OUTLOOK:
                messages = await email_service.provider_service.get_messages(top=1)
            elif email_account.provider == EmailProvider.IMAP:
                messages = await email_service.provider_service.get_messages(limit=1)
            else:
                return {"success": False, "error": "Unsupported provider"}
            
            return {
                "success": True,
                "message": "Connection successful",
                "test_results": {
                    "can_connect": True,
                    "message_count": len(messages)
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "test_results": {
                    "can_connect": False
                }
            }
