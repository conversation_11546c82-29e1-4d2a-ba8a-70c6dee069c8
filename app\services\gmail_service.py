"""
Gmail API service for email operations.
"""

import base64
import email
from typing import List, Dict, Any, Optional
from datetime import datetime
import aiohttp
from googleapiclient.discovery import build
from google.oauth2.credentials import Credentials

from app.models.email import Email, EmailAttachment
from app.models.email_account import EmailAccount
from app.core.config import settings


class GmailService:
    """Gmail API service for email operations."""
    
    def __init__(self, email_account: EmailAccount):
        self.email_account = email_account
        self.credentials = Credentials(
            token=email_account.access_token,
            refresh_token=email_account.refresh_token,
            token_uri="https://oauth2.googleapis.com/token",
            client_id=settings.GOOGLE_CLIENT_ID,
            client_secret=settings.GOOGLE_CLIENT_SECRET
        )
        self.service = build('gmail', 'v1', credentials=self.credentials)
    
    async def get_messages(self, query: str = "", max_results: int = 100) -> List[Dict[str, Any]]:
        """Get messages from Gmail."""
        try:
            results = self.service.users().messages().list(
                userId='me',
                q=query,
                maxResults=max_results
            ).execute()
            
            messages = results.get('messages', [])
            return messages
        except Exception as e:
            print(f"Error fetching messages: {e}")
            return []
    
    async def get_message_details(self, message_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed message information."""
        try:
            message = self.service.users().messages().get(
                userId='me',
                id=message_id,
                format='full'
            ).execute()
            
            return message
        except Exception as e:
            print(f"Error fetching message details: {e}")
            return None
    
    def parse_message(self, message_data: Dict[str, Any]) -> Dict[str, Any]:
        """Parse Gmail message data into standardized format."""
        payload = message_data.get('payload', {})
        headers = payload.get('headers', [])
        
        # Extract headers
        header_dict = {h['name'].lower(): h['value'] for h in headers}
        
        # Extract body
        body_text = ""
        body_html = ""
        attachments = []
        
        def extract_parts(parts):
            nonlocal body_text, body_html, attachments
            
            for part in parts:
                mime_type = part.get('mimeType', '')
                
                if mime_type == 'text/plain':
                    data = part.get('body', {}).get('data', '')
                    if data:
                        body_text = base64.urlsafe_b64decode(data).decode('utf-8')
                
                elif mime_type == 'text/html':
                    data = part.get('body', {}).get('data', '')
                    if data:
                        body_html = base64.urlsafe_b64decode(data).decode('utf-8')
                
                elif part.get('filename'):
                    # This is an attachment
                    attachments.append({
                        'filename': part['filename'],
                        'mime_type': mime_type,
                        'size': part.get('body', {}).get('size', 0),
                        'attachment_id': part.get('body', {}).get('attachmentId')
                    })
                
                # Recursively process nested parts
                if 'parts' in part:
                    extract_parts(part['parts'])
        
        # Extract body and attachments
        if 'parts' in payload:
            extract_parts(payload['parts'])
        else:
            # Single part message
            mime_type = payload.get('mimeType', '')
            if mime_type == 'text/plain':
                data = payload.get('body', {}).get('data', '')
                if data:
                    body_text = base64.urlsafe_b64decode(data).decode('utf-8')
            elif mime_type == 'text/html':
                data = payload.get('body', {}).get('data', '')
                if data:
                    body_html = base64.urlsafe_b64decode(data).decode('utf-8')
        
        # Parse date
        date_str = header_dict.get('date', '')
        received_at = None
        if date_str:
            try:
                received_at = email.utils.parsedate_to_datetime(date_str)
            except:
                received_at = datetime.utcnow()
        
        # Extract recipient emails
        to_emails = []
        cc_emails = []
        bcc_emails = []
        
        if header_dict.get('to'):
            to_emails = [addr.strip() for addr in header_dict['to'].split(',')]
        if header_dict.get('cc'):
            cc_emails = [addr.strip() for addr in header_dict['cc'].split(',')]
        if header_dict.get('bcc'):
            bcc_emails = [addr.strip() for addr in header_dict['bcc'].split(',')]
        
        return {
            'message_id': header_dict.get('message-id', ''),
            'thread_id': message_data.get('threadId', ''),
            'subject': header_dict.get('subject', ''),
            'sender_email': header_dict.get('from', ''),
            'sender_name': header_dict.get('from', '').split('<')[0].strip() if '<' in header_dict.get('from', '') else '',
            'recipient_emails': to_emails,
            'cc_emails': cc_emails,
            'bcc_emails': bcc_emails,
            'body_text': body_text,
            'body_html': body_html,
            'received_at': received_at,
            'labels': message_data.get('labelIds', []),
            'attachments': attachments,
            'has_attachments': len(attachments) > 0,
            'attachment_count': len(attachments),
            'is_read': 'UNREAD' not in message_data.get('labelIds', []),
            'is_starred': 'STARRED' in message_data.get('labelIds', []),
            'is_important': 'IMPORTANT' in message_data.get('labelIds', [])
        }
    
    async def download_attachment(self, message_id: str, attachment_id: str) -> Optional[bytes]:
        """Download email attachment."""
        try:
            attachment = self.service.users().messages().attachments().get(
                userId='me',
                messageId=message_id,
                id=attachment_id
            ).execute()
            
            data = attachment.get('data', '')
            if data:
                return base64.urlsafe_b64decode(data)
            
            return None
        except Exception as e:
            print(f"Error downloading attachment: {e}")
            return None
    
    async def send_message(self, to_emails: List[str], subject: str, body: str, 
                          cc_emails: List[str] = None, bcc_emails: List[str] = None) -> bool:
        """Send an email message."""
        try:
            message = email.mime.text.MIMEText(body)
            message['to'] = ', '.join(to_emails)
            message['subject'] = subject
            
            if cc_emails:
                message['cc'] = ', '.join(cc_emails)
            if bcc_emails:
                message['bcc'] = ', '.join(bcc_emails)
            
            raw_message = base64.urlsafe_b64encode(message.as_bytes()).decode()
            
            send_message = self.service.users().messages().send(
                userId='me',
                body={'raw': raw_message}
            ).execute()
            
            return True
        except Exception as e:
            print(f"Error sending message: {e}")
            return False
    
    async def mark_as_read(self, message_id: str) -> bool:
        """Mark message as read."""
        try:
            self.service.users().messages().modify(
                userId='me',
                id=message_id,
                body={'removeLabelIds': ['UNREAD']}
            ).execute()
            return True
        except Exception as e:
            print(f"Error marking message as read: {e}")
            return False
    
    async def add_labels(self, message_id: str, label_ids: List[str]) -> bool:
        """Add labels to a message."""
        try:
            self.service.users().messages().modify(
                userId='me',
                id=message_id,
                body={'addLabelIds': label_ids}
            ).execute()
            return True
        except Exception as e:
            print(f"Error adding labels: {e}")
            return False
    
    async def get_labels(self) -> List[Dict[str, Any]]:
        """Get all available labels."""
        try:
            results = self.service.users().labels().list(userId='me').execute()
            return results.get('labels', [])
        except Exception as e:
            print(f"Error fetching labels: {e}")
            return []
