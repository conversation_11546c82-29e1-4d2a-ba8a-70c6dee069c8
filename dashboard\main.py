"""
Main Streamlit dashboard for Email Management System.
"""

import streamlit as st
import requests
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import os
import sys

# Add the parent directory to the path to import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.config import settings

# Page configuration
st.set_page_config(
    page_title="Email Management System",
    page_icon="📧",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .sidebar-header {
        font-size: 1.2rem;
        font-weight: bold;
        color: #1f77b4;
        margin-bottom: 1rem;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'authenticated' not in st.session_state:
    st.session_state.authenticated = False
if 'access_token' not in st.session_state:
    st.session_state.access_token = None

def authenticate_user():
    """Handle user authentication."""
    st.sidebar.markdown('<div class="sidebar-header">🔐 Authentication</div>', unsafe_allow_html=True)
    
    if not st.session_state.authenticated:
        with st.sidebar.form("login_form"):
            email = st.text_input("Email")
            password = st.text_input("Password", type="password")
            submit_button = st.form_submit_button("Login")
            
            if submit_button and email and password:
                # TODO: Implement actual authentication with FastAPI backend
                # For now, use dummy authentication
                if email and password:
                    st.session_state.authenticated = True
                    st.session_state.access_token = "dummy_token"
                    st.success("Logged in successfully!")
                    st.rerun()
                else:
                    st.error("Invalid credentials")
        
        st.sidebar.markdown("---")
        st.sidebar.markdown("**OAuth Options:**")
        if st.sidebar.button("🔗 Login with Google"):
            st.info("Google OAuth integration coming soon!")
        if st.sidebar.button("🔗 Login with Microsoft"):
            st.info("Microsoft OAuth integration coming soon!")
    else:
        st.sidebar.success(f"✅ Authenticated")
        if st.sidebar.button("Logout"):
            st.session_state.authenticated = False
            st.session_state.access_token = None
            st.rerun()

def main_dashboard():
    """Main dashboard content."""
    st.markdown('<div class="main-header">📧 Email Management System</div>', unsafe_allow_html=True)
    
    # Sidebar navigation
    st.sidebar.markdown('<div class="sidebar-header">📊 Navigation</div>', unsafe_allow_html=True)
    page = st.sidebar.selectbox(
        "Select Page",
        ["Dashboard", "Email Analytics", "Classification", "Settings", "Account Management"]
    )
    
    if page == "Dashboard":
        show_dashboard()
    elif page == "Email Analytics":
        show_analytics()
    elif page == "Classification":
        show_classification()
    elif page == "Settings":
        show_settings()
    elif page == "Account Management":
        show_account_management()

def show_dashboard():
    """Show main dashboard with overview metrics."""
    st.header("📊 Dashboard Overview")
    
    # Metrics row
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            label="📧 Total Emails",
            value="1,234",
            delta="12 today"
        )
    
    with col2:
        st.metric(
            label="⚡ Processed Today",
            value="89",
            delta="15%"
        )
    
    with col3:
        st.metric(
            label="🎯 Classification Accuracy",
            value="94.2%",
            delta="2.1%"
        )
    
    with col4:
        st.metric(
            label="⏱️ Avg Response Time",
            value="2.3h",
            delta="-0.5h"
        )
    
    # Charts row
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("📈 Email Volume Trend")
        # Sample data for demonstration
        dates = pd.date_range(start='2024-01-01', end='2024-01-30', freq='D')
        volumes = [50 + i*2 + (i%7)*10 for i in range(len(dates))]
        df = pd.DataFrame({'Date': dates, 'Volume': volumes})
        
        fig = px.line(df, x='Date', y='Volume', title="Daily Email Volume")
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        st.subheader("🏷️ Email Categories")
        # Sample data for pie chart
        categories = ['Work', 'Personal', 'Newsletters', 'Spam', 'Urgent']
        values = [45, 25, 15, 10, 5]
        
        fig = px.pie(values=values, names=categories, title="Email Distribution by Category")
        st.plotly_chart(fig, use_container_width=True)
    
    # Recent emails table
    st.subheader("📬 Recent Emails")
    sample_emails = pd.DataFrame({
        'From': ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
        'Subject': ['Project Update', 'Weekly Tech News', 'Meeting Tomorrow'],
        'Category': ['Work', 'Newsletter', 'Work'],
        'Sentiment': ['Neutral', 'Positive', 'Urgent'],
        'Received': ['2 hours ago', '1 day ago', '3 hours ago']
    })
    st.dataframe(sample_emails, use_container_width=True)

def show_analytics():
    """Show detailed analytics page."""
    st.header("📊 Email Analytics")
    st.info("Detailed analytics dashboard coming soon!")

def show_classification():
    """Show email classification page."""
    st.header("🏷️ Email Classification")
    st.info("Email classification interface coming soon!")

def show_settings():
    """Show settings page."""
    st.header("⚙️ Settings")
    st.info("Settings configuration coming soon!")

def show_account_management():
    """Show account management page."""
    st.header("👤 Account Management")
    st.info("Account management interface coming soon!")

def main():
    """Main application entry point."""
    authenticate_user()
    
    if st.session_state.authenticated:
        main_dashboard()
    else:
        st.markdown('<div class="main-header">📧 Email Management System</div>', unsafe_allow_html=True)
        st.info("Please log in to access the dashboard.")
        
        # Show some public information
        st.markdown("## Features")
        st.markdown("""
        - 🔗 **Multi-provider Integration**: Gmail, Outlook, and IMAP support
        - 🤖 **AI-powered Classification**: Automatic email categorization
        - 📊 **Advanced Analytics**: Detailed insights and trends
        - 🔒 **Secure & Private**: End-to-end encryption and OAuth 2.0
        - ⚡ **Smart Automation**: Intelligent replies and organization
        """)

if __name__ == "__main__":
    main()
