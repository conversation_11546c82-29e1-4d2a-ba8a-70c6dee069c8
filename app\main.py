"""
Main FastAPI application entry point.
"""

from fastapi import <PERSON><PERSON><PERSON>, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>ear<PERSON>
import uvicorn

from app.core.config import settings
from app.core.database import engine, Base
from app.api import auth, emails, analytics, users
from app.core.security import get_current_user

# Create database tables
Base.metadata.create_all(bind=engine)

# Initialize FastAPI app
app = FastAPI(
    title="Email Management System",
    description="AI-powered email management with multi-provider support",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()

# Include API routers
app.include_router(auth.router, prefix="/api/v1/auth", tags=["authentication"])
app.include_router(
    emails.router, 
    prefix="/api/v1/emails", 
    tags=["emails"],
    dependencies=[Depends(get_current_user)]
)
app.include_router(
    analytics.router, 
    prefix="/api/v1/analytics", 
    tags=["analytics"],
    dependencies=[Depends(get_current_user)]
)
app.include_router(
    users.router, 
    prefix="/api/v1/users", 
    tags=["users"],
    dependencies=[Depends(get_current_user)]
)

@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "Email Management System API",
        "version": "1.0.0",
        "docs": "/docs",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "email-management-api"}

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
