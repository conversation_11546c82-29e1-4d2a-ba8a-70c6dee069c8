# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
streamlit==1.28.1

# Database
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9

# Background Tasks
celery==5.3.4
redis==5.0.1

# Email Integration
google-api-python-client==2.108.0
google-auth-httplib2==0.1.1
google-auth-oauthlib==1.1.0
msal==1.25.0
imapclient==2.3.1

# NLP and ML
spacy==3.7.2
nltk==3.8.1
scikit-learn==1.3.2
transformers==4.35.2
torch==2.1.1

# Data Processing
pandas==2.1.3
numpy==1.25.2
python-dateutil==2.8.2

# Security and Authentication
cryptography==41.0.8
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0
python-multipart==0.0.6

# Configuration and Environment
python-dotenv==1.0.0
pydantic==2.5.0
pydantic-settings==2.0.3

# Utilities
requests==2.31.0
aiofiles==23.2.1
python-magic==0.4.27

# Development
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0
