"""
Analytics models for tracking email patterns and user behavior.
"""

from sqlalchemy import <PERSON>umn, Integer, String, Boolean, DateTime, Text, ForeignKey, JSON, Float, Date
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from app.core.database import Base


class UserAnalytics(Base):
    """Daily analytics data for users."""
    
    __tablename__ = "user_analytics"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    date = Column(Date, nullable=False, index=True)
    
    # Email volume metrics
    emails_received = Column(Integer, default=0)
    emails_sent = Column(Integer, default=0)
    emails_processed = Column(Integer, default=0)
    emails_unread = Column(Integer, default=0)
    
    # Response metrics
    avg_response_time_hours = Column(Float, nullable=True)
    emails_responded = Column(Integer, default=0)
    response_rate = Column(Float, nullable=True)  # Percentage
    
    # Category distribution (JSON)
    category_distribution = Column(JSON, nullable=True)
    # Example: {"work": 45, "personal": 25, "newsletter": 15, "spam": 10, "urgent": 5}
    
    # Sentiment distribution (JSON)
    sentiment_distribution = Column(JSON, nullable=True)
    # Example: {"positive": 60, "neutral": 30, "negative": 8, "urgent": 2}
    
    # Time-based patterns (JSON)
    hourly_distribution = Column(JSON, nullable=True)  # Email count by hour
    
    # Productivity metrics
    inbox_zero_achieved = Column(Boolean, default=False)
    time_spent_minutes = Column(Integer, default=0)
    emails_archived = Column(Integer, default=0)
    emails_deleted = Column(Integer, default=0)
    
    # AI assistance metrics
    smart_replies_used = Column(Integer, default=0)
    auto_classifications = Column(Integer, default=0)
    rules_applied = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="analytics")
    
    def __repr__(self):
        return f"<UserAnalytics(user_id={self.user_id}, date={self.date}, received={self.emails_received})>"


class SenderAnalytics(Base):
    """Analytics for email senders to identify patterns."""
    
    __tablename__ = "sender_analytics"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    sender_email = Column(String(255), nullable=False, index=True)
    sender_name = Column(String(255), nullable=True)
    
    # Volume metrics
    total_emails = Column(Integer, default=0)
    emails_last_30_days = Column(Integer, default=0)
    emails_last_7_days = Column(Integer, default=0)
    
    # Response metrics
    emails_responded_to = Column(Integer, default=0)
    avg_response_time_hours = Column(Float, nullable=True)
    response_rate = Column(Float, nullable=True)
    
    # Engagement metrics
    emails_read = Column(Integer, default=0)
    emails_starred = Column(Integer, default=0)
    emails_archived = Column(Integer, default=0)
    emails_deleted = Column(Integer, default=0)
    
    # Classification patterns
    most_common_category = Column(String(50), nullable=True)
    category_distribution = Column(JSON, nullable=True)
    
    # Time patterns
    most_active_hour = Column(Integer, nullable=True)  # 0-23
    most_active_day = Column(Integer, nullable=True)   # 0-6 (Monday=0)
    
    # VIP status
    is_vip = Column(Boolean, default=False)
    vip_score = Column(Float, default=0.0)  # Calculated importance score
    
    # Last interaction
    last_email_at = Column(DateTime(timezone=True), nullable=True)
    last_response_at = Column(DateTime(timezone=True), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User")
    
    def __repr__(self):
        return f"<SenderAnalytics(sender='{self.sender_email}', total={self.total_emails}, vip={self.is_vip})>"


class EmailThread(Base):
    """Email thread tracking for conversation analysis."""
    
    __tablename__ = "email_threads"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    thread_id = Column(String(255), nullable=False, index=True)
    
    # Thread metadata
    subject = Column(Text, nullable=True)
    participants = Column(JSON, nullable=True)  # List of email addresses
    
    # Thread metrics
    email_count = Column(Integer, default=0)
    unread_count = Column(Integer, default=0)
    
    # Thread status
    is_active = Column(Boolean, default=True)
    requires_response = Column(Boolean, default=False)
    
    # Time tracking
    started_at = Column(DateTime(timezone=True), nullable=True)
    last_activity_at = Column(DateTime(timezone=True), nullable=True)
    avg_response_time_hours = Column(Float, nullable=True)
    
    # Classification
    thread_category = Column(String(50), nullable=True)
    thread_sentiment = Column(String(20), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User")
    
    def __repr__(self):
        return f"<EmailThread(id={self.id}, thread_id='{self.thread_id}', emails={self.email_count})>"


class SystemMetrics(Base):
    """System-wide metrics for monitoring and optimization."""
    
    __tablename__ = "system_metrics"
    
    id = Column(Integer, primary_key=True, index=True)
    date = Column(Date, nullable=False, index=True)
    
    # Processing metrics
    emails_processed = Column(Integer, default=0)
    processing_time_avg_ms = Column(Float, nullable=True)
    classification_accuracy = Column(Float, nullable=True)
    
    # Error metrics
    processing_errors = Column(Integer, default=0)
    sync_errors = Column(Integer, default=0)
    api_errors = Column(Integer, default=0)
    
    # Performance metrics
    api_response_time_avg_ms = Column(Float, nullable=True)
    database_query_time_avg_ms = Column(Float, nullable=True)
    
    # Usage metrics
    active_users = Column(Integer, default=0)
    total_api_calls = Column(Integer, default=0)
    
    # AI metrics
    smart_replies_generated = Column(Integer, default=0)
    smart_replies_used = Column(Integer, default=0)
    classification_confidence_avg = Column(Float, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    def __repr__(self):
        return f"<SystemMetrics(date={self.date}, processed={self.emails_processed}, accuracy={self.classification_accuracy})>"
